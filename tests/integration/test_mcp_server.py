"""
Integration tests for MCP Bible Study Server.

Tests the full MCP protocol implementation including client-server interaction,
tool calls, and error handling.
"""

import asyncio
import json
import tempfile
from pathlib import Path
from unittest.mock import AsyncMock, Mock, patch

import pytest
import mcp.types as types
from mcp.client import Client
from mcp.client.stdio import StdioClientTransport

from src.mcp_server import BibleStudyMCPServer, ServerConfig


class TestMCPServerIntegration:
    """Integration tests for the MCP server."""
    
    @pytest.fixture
    async def temp_config(self):
        """Create test configuration with temporary directories."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            config = ServerConfig(
                name="test-bible-study-server",
                version="0.1.0-test",
                documents_dir=temp_path / "docs",
                vector_store_path=temp_path / "vectors",
                use_fake_embeddings=True,
                chunk_size=100,
                chunk_overlap=10,
                similarity_search_k=2,
                log_level="DEBUG"
            )
            config.ensure_directories()
            yield config
    
    @pytest.fixture
    async def server(self, temp_config):
        """Create MCP server instance."""
        return BibleStudyMCPServer(temp_config)
    
    async def test_server_initialization(self, server, temp_config):
        """Test server initialization."""
        assert server.config == temp_config
        assert server.bible_study_service is not None
        assert server.vector_store_service is not None
        assert server.server is not None
    
    async def test_list_tools(self, server):
        """Test listing available tools."""
        # Get the list_tools handler
        tools = await server.server._request_handlers["tools/list"]()
        
        assert len(tools) == 2
        
        # Check study_method_overview tool
        study_tool = next(tool for tool in tools if tool.name == "study_method_overview")
        assert study_tool.name == "study_method_overview"
        assert "圣经研习方法" in study_tool.description
        assert study_tool.inputSchema["type"] == "object"
        assert study_tool.inputSchema["required"] == []
        
        # Check semantic_context tool
        context_tool = next(tool for tool in tools if tool.name == "semantic_context")
        assert context_tool.name == "semantic_context"
        assert "语义搜索" in context_tool.description
        assert context_tool.inputSchema["required"] == ["query"]
        assert "query" in context_tool.inputSchema["properties"]
    
    async def test_call_study_method_overview_tool(self, server):
        """Test calling the study_method_overview tool."""
        # Get the call_tool handler
        call_tool_handler = server.server._request_handlers["tools/call"]
        
        result = await call_tool_handler("study_method_overview", {})
        
        assert isinstance(result, list)
        assert len(result) == 1
        assert isinstance(result[0], types.TextContent)
        assert result[0].type == "text"
        
        # Check content
        content = result[0].text
        assert isinstance(content, str)
        assert len(content) > 0
        assert "观察法" in content
        assert "解释法" in content
        assert "应用法" in content
    
    async def test_call_semantic_context_tool_valid_query(self, server):
        """Test calling the semantic_context tool with valid query."""
        # Mock the vector store service to return predictable results
        mock_results = [
            {
                "text": "测试文本内容1",
                "metadata": {"source": "test1.pdf", "page": 1},
                "score": 0.95
            },
            {
                "text": "测试文本内容2", 
                "metadata": {"source": "test2.pdf", "page": 2},
                "score": 0.85
            }
        ]
        
        server.vector_store_service.semantic_search = AsyncMock(return_value=mock_results)
        
        # Get the call_tool handler
        call_tool_handler = server.server._request_handlers["tools/call"]
        
        result = await call_tool_handler("semantic_context", {"query": "信仰问题"})
        
        assert isinstance(result, list)
        assert len(result) == 1
        assert isinstance(result[0], types.TextContent)
        assert result[0].type == "text"
        
        # Parse the JSON content
        content = json.loads(result[0].text)
        assert len(content) == 2
        assert content[0]["text"] == "测试文本内容1"
        assert content[0]["metadata"]["source"] == "test1.pdf"
        assert content[0]["score"] == 0.95
    
    async def test_call_semantic_context_tool_missing_query(self, server):
        """Test calling semantic_context tool without query parameter."""
        call_tool_handler = server.server._request_handlers["tools/call"]
        
        result = await call_tool_handler("semantic_context", {})
        
        assert isinstance(result, list)
        assert len(result) == 1
        assert isinstance(result[0], types.TextContent)
        assert "Missing required argument: query" in result[0].text
    
    async def test_call_semantic_context_tool_invalid_query(self, server):
        """Test calling semantic_context tool with invalid query."""
        call_tool_handler = server.server._request_handlers["tools/call"]
        
        # Test with empty query
        result = await call_tool_handler("semantic_context", {"query": ""})
        
        assert isinstance(result, list)
        assert len(result) == 1
        assert isinstance(result[0], types.TextContent)
        assert "Invalid query" in result[0].text
    
    async def test_call_unknown_tool(self, server):
        """Test calling an unknown tool."""
        call_tool_handler = server.server._request_handlers["tools/call"]
        
        result = await call_tool_handler("unknown_tool", {})
        
        assert isinstance(result, list)
        assert len(result) == 1
        assert isinstance(result[0], types.TextContent)
        assert "Unknown tool: unknown_tool" in result[0].text
    
    async def test_tool_call_with_service_error(self, server):
        """Test tool call when service raises an error."""
        # Mock the service to raise an exception
        server.bible_study_service.get_study_method_overview = Mock(
            side_effect=Exception("Service error")
        )
        
        call_tool_handler = server.server._request_handlers["tools/call"]
        
        result = await call_tool_handler("study_method_overview", {})
        
        assert isinstance(result, list)
        assert len(result) == 1
        assert isinstance(result[0], types.TextContent)
        assert "Error executing tool 'study_method_overview'" in result[0].text
        assert "Service error" in result[0].text
    
    async def test_server_info(self, server, temp_config):
        """Test getting server information."""
        info = server.get_server_info()
        
        assert info["name"] == temp_config.name
        assert info["version"] == temp_config.version
        assert "config" in info
        assert "vector_store_info" in info
        
        # Check config info
        config_info = info["config"]
        assert config_info["embedding_model"] == temp_config.embedding_model_name
        assert config_info["use_fake_embeddings"] == temp_config.use_fake_embeddings
        assert config_info["log_level"] == temp_config.log_level
    
    async def test_error_handling_in_tool_calls(self, server):
        """Test comprehensive error handling in tool calls."""
        call_tool_handler = server.server._request_handlers["tools/call"]
        
        # Test various error scenarios
        error_scenarios = [
            ("semantic_context", {"query": None}, "Query must be a string"),
            ("semantic_context", {"query": 123}, "Query must be a string"),
            ("semantic_context", {"query": "a"}, "Invalid query"),
            ("semantic_context", {"query": "a" * 1001}, "Invalid query"),
        ]
        
        for tool_name, arguments, expected_error in error_scenarios:
            result = await call_tool_handler(tool_name, arguments)
            
            assert isinstance(result, list)
            assert len(result) == 1
            assert isinstance(result[0], types.TextContent)
            assert expected_error in result[0].text or "Error executing tool" in result[0].text
    
    async def test_concurrent_tool_calls(self, server):
        """Test handling concurrent tool calls."""
        call_tool_handler = server.server._request_handlers["tools/call"]
        
        # Create multiple concurrent calls
        tasks = [
            call_tool_handler("study_method_overview", {}),
            call_tool_handler("study_method_overview", {}),
            call_tool_handler("study_method_overview", {}),
        ]
        
        results = await asyncio.gather(*tasks)
        
        # All calls should succeed
        assert len(results) == 3
        for result in results:
            assert isinstance(result, list)
            assert len(result) == 1
            assert isinstance(result[0], types.TextContent)
            assert "观察法" in result[0].text
    
    async def test_tool_call_context_handling(self, server):
        """Test that tool calls properly handle context."""
        # Mock the service to capture the context
        captured_context = None
        
        def capture_context(context):
            nonlocal captured_context
            captured_context = context
            return "Test result"
        
        server.bible_study_service.get_study_method_overview = capture_context
        
        call_tool_handler = server.server._request_handlers["tools/call"]
        await call_tool_handler("study_method_overview", {})
        
        # Context should have been passed
        assert captured_context is not None
        # Context should be a ToolCallContext instance
        from src.mcp_server.config import ToolCallContext
        assert isinstance(captured_context, ToolCallContext)
