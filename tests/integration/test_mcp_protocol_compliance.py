"""
MCP Protocol Compliance Tests.

Tests to ensure the server properly implements the MCP protocol specification,
including proper message formats, error handling, and capability negotiation.
"""

import asyncio
import json
import tempfile
from pathlib import Path
from unittest.mock import AsyncMock, Mock

import pytest
import mcp.types as types

from src.mcp_server import BibleStudyMCPServer, ServerConfig


class TestMCPProtocolCompliance:
    """Test MCP protocol compliance."""
    
    @pytest.fixture
    async def temp_config(self):
        """Create test configuration."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            config = ServerConfig(
                name="test-mcp-server",
                version="1.0.0",
                documents_dir=temp_path / "docs",
                vector_store_path=temp_path / "vectors",
                use_fake_embeddings=True,
                log_level="DEBUG"
            )
            config.ensure_directories()
            yield config
    
    @pytest.fixture
    async def server(self, temp_config):
        """Create server instance."""
        return BibleStudyMCPServer(temp_config)
    
    async def test_server_capabilities(self, server):
        """Test that server properly declares its capabilities."""
        # The server should have tools capability
        # Note: In the official MCP SDK, capabilities are handled automatically
        # but we should ensure our tools are properly registered
        
        tools = await server.server._request_handlers["tools/list"]()
        assert len(tools) > 0
        
        # Each tool should have proper schema
        for tool in tools:
            assert isinstance(tool, types.Tool)
            assert tool.name
            assert tool.description
            assert tool.inputSchema
            assert tool.inputSchema.get("type") == "object"
            assert "properties" in tool.inputSchema or tool.inputSchema.get("required") == []
    
    async def test_tool_schema_compliance(self, server):
        """Test that tool schemas comply with JSON Schema standards."""
        tools = await server.server._request_handlers["tools/list"]()
        
        for tool in tools:
            schema = tool.inputSchema
            
            # Must have type
            assert schema.get("type") == "object"
            
            # Properties should be properly defined
            if "properties" in schema:
                for prop_name, prop_schema in schema["properties"].items():
                    assert "type" in prop_schema
                    assert "description" in prop_schema
            
            # Required fields should be a list
            if "required" in schema:
                assert isinstance(schema["required"], list)
    
    async def test_tool_call_response_format(self, server):
        """Test that tool call responses follow MCP format."""
        call_tool_handler = server.server._request_handlers["tools/call"]
        
        # Test study_method_overview
        result = await call_tool_handler("study_method_overview", {})
        
        assert isinstance(result, list)
        assert len(result) > 0
        
        for content in result:
            assert isinstance(content, types.TextContent)
            assert content.type == "text"
            assert isinstance(content.text, str)
    
    async def test_error_response_format(self, server):
        """Test that error responses follow MCP format."""
        call_tool_handler = server.server._request_handlers["tools/call"]
        
        # Call unknown tool
        result = await call_tool_handler("nonexistent_tool", {})
        
        # Should return error as text content, not raise exception
        assert isinstance(result, list)
        assert len(result) == 1
        assert isinstance(result[0], types.TextContent)
        assert "Unknown tool" in result[0].text or "Error" in result[0].text
    
    async def test_tool_parameter_validation(self, server):
        """Test parameter validation for tools."""
        call_tool_handler = server.server._request_handlers["tools/call"]
        
        # Test semantic_context with missing required parameter
        result = await call_tool_handler("semantic_context", {})
        assert isinstance(result, list)
        assert "Missing required argument" in result[0].text
        
        # Test semantic_context with invalid parameter type
        result = await call_tool_handler("semantic_context", {"query": 123})
        assert isinstance(result, list)
        assert "Query must be a string" in result[0].text
    
    async def test_tool_call_idempotency(self, server):
        """Test that tool calls are idempotent when appropriate."""
        call_tool_handler = server.server._request_handlers["tools/call"]
        
        # study_method_overview should return the same result
        result1 = await call_tool_handler("study_method_overview", {})
        result2 = await call_tool_handler("study_method_overview", {})
        
        assert result1[0].text == result2[0].text
    
    async def test_concurrent_tool_calls(self, server):
        """Test handling of concurrent tool calls."""
        call_tool_handler = server.server._request_handlers["tools/call"]
        
        # Create multiple concurrent calls
        tasks = []
        for i in range(5):
            task = call_tool_handler("study_method_overview", {})
            tasks.append(task)
        
        results = await asyncio.gather(*tasks)
        
        # All should succeed
        assert len(results) == 5
        for result in results:
            assert isinstance(result, list)
            assert len(result) == 1
            assert isinstance(result[0], types.TextContent)
    
    async def test_tool_call_with_large_response(self, server):
        """Test handling of large responses."""
        call_tool_handler = server.server._request_handlers["tools/call"]
        
        result = await call_tool_handler("study_method_overview", {})
        
        # Should handle large text responses properly
        assert isinstance(result, list)
        assert len(result) == 1
        text_content = result[0].text
        assert len(text_content) > 100  # Should be substantial content
        
        # Should be valid UTF-8
        text_content.encode('utf-8')
    
    async def test_json_serialization_compliance(self, server):
        """Test that all responses can be JSON serialized."""
        call_tool_handler = server.server._request_handlers["tools/call"]
        
        # Test all tools
        tools = await server.server._request_handlers["tools/list"]()
        
        for tool in tools:
            if tool.name == "study_method_overview":
                result = await call_tool_handler(tool.name, {})
            elif tool.name == "semantic_context":
                # Mock the vector store for this test
                server.vector_store_service.semantic_search = AsyncMock(return_value=[
                    {"text": "测试内容", "metadata": {"source": "test.pdf"}, "score": 0.9}
                ])
                result = await call_tool_handler(tool.name, {"query": "测试查询"})
            else:
                continue
            
            # Should be JSON serializable
            for content in result:
                content_dict = {
                    "type": content.type,
                    "text": content.text
                }
                json.dumps(content_dict, ensure_ascii=False)
    
    async def test_unicode_handling(self, server):
        """Test proper Unicode/Chinese text handling."""
        call_tool_handler = server.server._request_handlers["tools/call"]
        
        # Test Chinese query
        server.vector_store_service.semantic_search = AsyncMock(return_value=[
            {"text": "中文测试内容", "metadata": {"source": "测试.pdf"}, "score": 0.9}
        ])
        
        result = await call_tool_handler("semantic_context", {"query": "中文查询测试"})
        
        assert isinstance(result, list)
        assert len(result) == 1
        
        # Parse the JSON response
        response_data = json.loads(result[0].text)
        assert response_data[0]["text"] == "中文测试内容"
        assert response_data[0]["metadata"]["source"] == "测试.pdf"
    
    async def test_error_boundary_handling(self, server):
        """Test error boundary handling."""
        call_tool_handler = server.server._request_handlers["tools/call"]
        
        # Mock service to raise various types of errors
        original_method = server.bible_study_service.get_study_method_overview
        
        # Test ValueError
        server.bible_study_service.get_study_method_overview = Mock(
            side_effect=ValueError("Test value error")
        )
        result = await call_tool_handler("study_method_overview", {})
        assert "Error executing tool" in result[0].text
        
        # Test generic Exception
        server.bible_study_service.get_study_method_overview = Mock(
            side_effect=Exception("Test generic error")
        )
        result = await call_tool_handler("study_method_overview", {})
        assert "Error executing tool" in result[0].text
        
        # Restore original method
        server.bible_study_service.get_study_method_overview = original_method
    
    async def test_tool_metadata_consistency(self, server):
        """Test that tool metadata is consistent."""
        tools = await server.server._request_handlers["tools/list"]()
        
        # Check that we have expected tools
        tool_names = [tool.name for tool in tools]
        assert "study_method_overview" in tool_names
        assert "semantic_context" in tool_names
        
        # Check tool descriptions are in Chinese (appropriate for this server)
        for tool in tools:
            if tool.name in ["study_method_overview", "semantic_context"]:
                # Should contain Chinese characters
                assert any('\u4e00' <= char <= '\u9fff' for char in tool.description)
    
    async def test_server_info_structure(self, server):
        """Test server info structure for debugging."""
        info = server.get_server_info()
        
        # Should have required fields
        required_fields = ["name", "version", "config", "vector_store_info"]
        for field in required_fields:
            assert field in info
        
        # Config should have expected structure
        config = info["config"]
        config_fields = ["documents_dir", "vector_store_path", "embedding_model", "log_level"]
        for field in config_fields:
            assert field in config
        
        # Vector store info should have status
        assert "status" in info["vector_store_info"]
