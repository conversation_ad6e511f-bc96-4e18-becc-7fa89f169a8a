"""
Unit tests for Vector Store Service.

Tests the VectorStoreService class and its methods.
"""

import tempfile
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

import pytest

from src.mcp_server.config import ServerConfig, ToolCallContext
from src.mcp_server.services import VectorStoreService


class TestVectorStoreService:
    """Test cases for VectorStoreService class."""
    
    @pytest.fixture
    def temp_config(self):
        """Create test configuration with temporary directories."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            config = ServerConfig(
                documents_dir=temp_path / "docs",
                vector_store_path=temp_path / "vectors",
                use_fake_embeddings=True,
                chunk_size=100,
                chunk_overlap=10,
                similarity_search_k=2
            )
            config.ensure_directories()
            yield config
    
    @pytest.fixture
    def context(self):
        """Create test context."""
        return ToolCallContext(request_id="test-123")
    
    def test_service_initialization_with_fake_embeddings(self, temp_config):
        """Test service initialization with fake embeddings."""
        service = VectorStoreService(temp_config)
        
        assert service.config == temp_config
        assert service.embeddings is not None
        assert service.vector_store is not None
    
    @patch('src.mcp_server.services.vector_store_service.HuggingFaceEmbeddings')
    @patch('src.mcp_server.services.vector_store_service.FAISS')
    def test_service_initialization_with_real_embeddings(self, mock_faiss, mock_embeddings, temp_config):
        """Test service initialization with real embeddings."""
        temp_config.use_fake_embeddings = False
        mock_embeddings.return_value = Mock()
        mock_faiss.from_texts.return_value = Mock()

        service = VectorStoreService(temp_config)
        
        assert service.config == temp_config
        mock_embeddings.assert_called_once_with(
            model_name=temp_config.embedding_model_name
        )
    
    def test_vector_store_exists_false(self, temp_config):
        """Test vector store existence check when files don't exist."""
        service = VectorStoreService(temp_config)
        
        # Remove vector store files if they exist
        if temp_config.vector_store_path.exists():
            import shutil
            shutil.rmtree(temp_config.vector_store_path)
        
        assert not service._vector_store_exists()
    
    def test_get_pdf_files_empty_directory(self, temp_config):
        """Test getting PDF files from empty directory."""
        service = VectorStoreService(temp_config)
        pdf_files = service._get_pdf_files()
        
        assert pdf_files == []
    
    def test_get_pdf_files_with_files(self, temp_config):
        """Test getting PDF files when files exist."""
        # Create test files
        (temp_config.documents_dir / "test1.pdf").touch()
        (temp_config.documents_dir / "test2.PDF").touch()  # Different case
        (temp_config.documents_dir / "test.txt").touch()  # Not PDF
        (temp_config.documents_dir / "subdir").mkdir()  # Directory
        
        service = VectorStoreService(temp_config)
        pdf_files = service._get_pdf_files()
        
        assert len(pdf_files) == 2
        pdf_names = [f.name for f in pdf_files]
        assert "test1.pdf" in pdf_names
        assert "test2.PDF" in pdf_names
        assert "test.txt" not in pdf_names
    
    def test_semantic_search_invalid_query(self, temp_config):
        """Test semantic search with invalid queries."""
        service = VectorStoreService(temp_config)
        context = ToolCallContext()
        
        # Test empty query
        with pytest.raises(ValueError, match="Query must be a non-empty string"):
            service.semantic_search("", context)
        
        # Test None query
        with pytest.raises(ValueError, match="Query must be a non-empty string"):
            service.semantic_search(None, context)
        
        # Test non-string query
        with pytest.raises(ValueError, match="Query must be a non-empty string"):
            service.semantic_search(123, context)
    
    def test_semantic_search_valid_query(self, temp_config, context):
        """Test semantic search with valid query."""
        service = VectorStoreService(temp_config)
        
        # Mock the vector store similarity search
        mock_doc1 = Mock()
        mock_doc1.page_content = "Test content 1"
        mock_doc1.metadata = {"source": "test1.pdf"}
        mock_doc1.score = None
        
        mock_doc2 = Mock()
        mock_doc2.page_content = "Test content 2"
        mock_doc2.metadata = {"source": "test2.pdf"}
        mock_doc2.score = 0.95
        
        service.vector_store.similarity_search = Mock(return_value=[mock_doc1, mock_doc2])
        
        results = service.semantic_search("test query", context)
        
        assert len(results) == 2
        assert results[0]["text"] == "Test content 1"
        assert results[0]["metadata"]["source"] == "test1.pdf"
        assert results[0]["score"] is None
        
        assert results[1]["text"] == "Test content 2"
        assert results[1]["metadata"]["source"] == "test2.pdf"
        assert results[1]["score"] == 0.95
        
        # Verify the search was called correctly
        service.vector_store.similarity_search.assert_called_once_with(
            "test query",
            k=temp_config.similarity_search_k
        )
    
    def test_semantic_search_vector_store_error(self, temp_config, context):
        """Test semantic search when vector store raises an error."""
        service = VectorStoreService(temp_config)
        
        # Mock vector store to raise an exception
        service.vector_store.similarity_search = Mock(side_effect=Exception("Search failed"))
        
        with pytest.raises(ValueError, match="Semantic search failed: Search failed"):
            service.semantic_search("test query", context)
    
    def test_get_vector_store_info_initialized(self, temp_config):
        """Test getting vector store info when initialized."""
        service = VectorStoreService(temp_config)
        
        # Mock the vector store index
        service.vector_store.index = Mock()
        service.vector_store.index.ntotal = 100
        
        info = service.get_vector_store_info()
        
        assert info["status"] == "initialized"
        assert info["embedding_model"] == temp_config.embedding_model_name
        assert info["use_fake_embeddings"] == temp_config.use_fake_embeddings
        assert info["document_count"] == 100
    
    def test_get_vector_store_info_not_initialized(self, temp_config):
        """Test getting vector store info when not initialized."""
        service = VectorStoreService(temp_config)
        service.vector_store = None
        
        info = service.get_vector_store_info()
        
        assert info["status"] == "not_initialized"
    
    def test_get_vector_store_info_error(self, temp_config):
        """Test getting vector store info when an error occurs."""
        service = VectorStoreService(temp_config)

        # Mock to raise an exception during info gathering
        with patch.object(service.config, 'embedding_model_name', side_effect=Exception("Config error")):
            info = service.get_vector_store_info()

            assert info["status"] == "error"
            assert "Config error" in info["error"]
    
    @patch('src.mcp_server.services.vector_store_service.PyMuPDFLoader')
    @patch('src.mcp_server.services.vector_store_service.FAISS')
    def test_load_documents_success(self, mock_faiss, mock_loader, temp_config):
        """Test successful document loading."""
        # Create test PDF file
        test_pdf = temp_config.documents_dir / "test.pdf"
        test_pdf.touch()

        # Mock the loader
        mock_doc = Mock()
        mock_doc.page_content = "Test content"
        mock_doc.metadata = {"source": str(test_pdf)}

        mock_loader_instance = Mock()
        mock_loader_instance.load.return_value = [mock_doc]
        mock_loader.return_value = mock_loader_instance

        # Mock FAISS to avoid vector store creation issues
        mock_faiss.from_texts.return_value = Mock()

        service = VectorStoreService(temp_config)
        documents = service._load_documents([test_pdf])

        assert len(documents) == 1
        mock_loader.assert_called_once_with(str(test_pdf))
        mock_loader_instance.load.assert_called_once()
    
    @patch('src.mcp_server.services.vector_store_service.PyMuPDFLoader')
    def test_load_documents_error(self, mock_loader, temp_config):
        """Test document loading with errors."""
        # Create test PDF file
        test_pdf = temp_config.documents_dir / "test.pdf"
        test_pdf.touch()
        
        # Mock the loader to raise an exception
        mock_loader.side_effect = Exception("PDF loading failed")
        
        service = VectorStoreService(temp_config)
        documents = service._load_documents([test_pdf])
        
        # Should return empty list when loading fails
        assert documents == []
    
    def test_split_documents(self, temp_config):
        """Test document splitting."""
        service = VectorStoreService(temp_config)
        
        # Create mock documents
        mock_doc = Mock()
        mock_doc.page_content = "A" * 200  # Long content to trigger splitting
        mock_doc.metadata = {"source": "test.pdf"}
        
        with patch('src.mcp_server.services.vector_store_service.RecursiveCharacterTextSplitter') as mock_splitter:
            mock_splitter_instance = Mock()
            mock_splitter_instance.split_documents.return_value = [mock_doc, mock_doc]
            mock_splitter.return_value = mock_splitter_instance
            
            chunks = service._split_documents([mock_doc])
            
            assert len(chunks) == 2
            mock_splitter.assert_called_once_with(
                chunk_size=temp_config.chunk_size,
                chunk_overlap=temp_config.chunk_overlap,
                separators=["\n\n", "\n", "。", "，", " ", ""],
                length_function=len,
            )
