"""
Unit tests for Bible Study Service.

Tests the BibleStudyService class and its methods.
"""

import pytest

from src.mcp_server.config import ServerConfig, ToolCallContext
from src.mcp_server.services import BibleStudyService


class TestBibleStudyService:
    """Test cases for BibleStudyService class."""
    
    @pytest.fixture
    def config(self):
        """Create test configuration."""
        return ServerConfig(use_fake_embeddings=True)
    
    @pytest.fixture
    def service(self, config):
        """Create Bible study service instance."""
        return BibleStudyService(config)
    
    @pytest.fixture
    def context(self):
        """Create test context."""
        return ToolCallContext(request_id="test-123")
    
    def test_service_initialization(self, config):
        """Test service initialization."""
        service = BibleStudyService(config)
        assert service.config == config
    
    def test_get_study_method_overview(self, service, context):
        """Test getting study method overview."""
        overview = service.get_study_method_overview(context)
        
        # Should return a string
        assert isinstance(overview, str)
        assert len(overview) > 0
        
        # Should contain expected Chinese content
        expected_terms = ["观察法", "解释法", "应用法", "祷告默想", "小组讨论"]
        for term in expected_terms:
            assert term in overview
    
    def test_get_study_method_overview_error_handling(self, service):
        """Test error handling in study method overview."""
        # Test with invalid context (should still work)
        overview = service.get_study_method_overview(None)
        assert isinstance(overview, str)
        assert len(overview) > 0
    
    def test_validate_query_valid_queries(self, service):
        """Test query validation with valid queries."""
        valid_queries = [
            "信仰",
            "关于信仰的问题",
            "圣经学习方法",
            "How to study the Bible",
            "a" * 100,  # Long but valid query
        ]
        
        for query in valid_queries:
            assert service.validate_query(query) is True
    
    def test_validate_query_invalid_queries(self, service):
        """Test query validation with invalid queries."""
        invalid_queries = [
            "",  # Empty string
            "   ",  # Whitespace only
            "a",  # Too short
            None,  # None value
            123,  # Not a string
            [],  # Not a string
            "a" * 1001,  # Too long
        ]
        
        for query in invalid_queries:
            assert service.validate_query(query) is False
    
    def test_format_search_results_empty(self, service):
        """Test formatting empty search results."""
        results = service.format_search_results([])
        assert results == []
    
    def test_format_search_results_with_data(self, service):
        """Test formatting search results with data."""
        input_results = [
            {
                "text": "Sample text 1",
                "metadata": {"source": "doc1.pdf", "page": 1},
                "score": 0.95
            },
            {
                "text": "Sample text 2",
                "metadata": {"source": "doc2.pdf", "page": 2},
                # No score provided
            }
        ]
        
        formatted = service.format_search_results(input_results)
        
        assert len(formatted) == 2
        
        # Check first result
        assert formatted[0]["text"] == "Sample text 1"
        assert formatted[0]["metadata"]["source"] == "doc1.pdf"
        assert formatted[0]["score"] == 0.95
        
        # Check second result
        assert formatted[1]["text"] == "Sample text 2"
        assert formatted[1]["metadata"]["source"] == "doc2.pdf"
        assert formatted[1]["score"] is None
    
    def test_format_search_results_missing_fields(self, service):
        """Test formatting search results with missing fields."""
        input_results = [
            {},  # Empty dict
            {"text": "Only text"},  # Missing metadata
            {"metadata": {"source": "doc.pdf"}},  # Missing text
        ]
        
        formatted = service.format_search_results(input_results)
        
        assert len(formatted) == 3
        
        # Check handling of missing fields
        assert formatted[0]["text"] == ""
        assert formatted[0]["metadata"] == {}
        assert formatted[0]["score"] is None
        
        assert formatted[1]["text"] == "Only text"
        assert formatted[1]["metadata"] == {}
        
        assert formatted[2]["text"] == ""
        assert formatted[2]["metadata"]["source"] == "doc.pdf"
    
    def test_get_study_tips(self, service):
        """Test getting study tips."""
        tips = service.get_study_tips()
        
        # Should return a list
        assert isinstance(tips, list)
        assert len(tips) > 0
        
        # All items should be strings
        for tip in tips:
            assert isinstance(tip, str)
            assert len(tip) > 0
        
        # Should contain expected Chinese content
        expected_content = ["祷告", "研经", "经文"]
        found_content = any(
            any(term in tip for term in expected_content)
            for tip in tips
        )
        assert found_content, "Tips should contain expected Chinese content"
    
    def test_service_consistency(self, service, context):
        """Test that service methods return consistent results."""
        # Multiple calls should return the same result
        overview1 = service.get_study_method_overview(context)
        overview2 = service.get_study_method_overview(context)
        assert overview1 == overview2
        
        tips1 = service.get_study_tips()
        tips2 = service.get_study_tips()
        assert tips1 == tips2
    
    def test_service_with_different_configs(self):
        """Test service behavior with different configurations."""
        config1 = ServerConfig(use_fake_embeddings=True)
        config2 = ServerConfig(use_fake_embeddings=False, chunk_size=500)
        
        service1 = BibleStudyService(config1)
        service2 = BibleStudyService(config2)
        
        # Both should work
        context = ToolCallContext()
        overview1 = service1.get_study_method_overview(context)
        overview2 = service2.get_study_method_overview(context)
        
        # Content should be the same regardless of config
        assert overview1 == overview2
