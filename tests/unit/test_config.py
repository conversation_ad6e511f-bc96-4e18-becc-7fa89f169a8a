"""
Unit tests for configuration management.

Tests the ServerConfig class and related configuration utilities.
"""

import os
import tempfile
from pathlib import Path
from unittest.mock import patch

import pytest

from src.mcp_server.config import ServerConfig, ToolCallContext


class TestServerConfig:
    """Test cases for ServerConfig class."""
    
    def test_default_config(self):
        """Test default configuration values."""
        config = ServerConfig()
        
        assert config.name == "bible-study-mcp-server"
        assert config.version == "0.1.0"
        assert config.embedding_model_name == "jinaai/jina-embeddings-v2-base-zh"
        assert config.use_fake_embeddings is False
        assert config.chunk_size == 1000
        assert config.chunk_overlap == 100
        assert config.similarity_search_k == 3
        assert config.log_level == "INFO"
    
    def test_config_from_env(self):
        """Test configuration loading from environment variables."""
        env_vars = {
            "MCP_SERVER_NAME": "test-server",
            "MCP_SERVER_VERSION": "1.0.0",
            "MCP_EMBEDDING_MODEL": "test-model",
            "MCP_USE_FAKE_EMBEDDINGS": "true",
            "MCP_CHUNK_SIZE": "500",
            "MCP_CHUNK_OVERLAP": "50",
            "MCP_SIMILARITY_K": "5",
            "MCP_LOG_LEVEL": "DEBUG",
        }
        
        with patch.dict(os.environ, env_vars):
            config = ServerConfig.from_env()
            
            assert config.name == "test-server"
            assert config.version == "1.0.0"
            assert config.embedding_model_name == "test-model"
            assert config.use_fake_embeddings is True
            assert config.chunk_size == 500
            assert config.chunk_overlap == 50
            assert config.similarity_search_k == 5
            assert config.log_level == "DEBUG"
    
    def test_config_with_custom_paths(self):
        """Test configuration with custom paths."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            docs_dir = temp_path / "docs"
            vector_dir = temp_path / "vectors"
            
            config = ServerConfig(
                documents_dir=docs_dir,
                vector_store_path=vector_dir
            )
            
            assert config.documents_dir == docs_dir
            assert config.vector_store_path == vector_dir
    
    def test_ensure_directories(self):
        """Test directory creation."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            docs_dir = temp_path / "new_docs"
            vector_dir = temp_path / "new_vectors" / "subdir"
            
            config = ServerConfig(
                documents_dir=docs_dir,
                vector_store_path=vector_dir
            )
            
            # Directories should not exist initially
            assert not docs_dir.exists()
            assert not vector_dir.parent.exists()
            
            # Create directories
            config.ensure_directories()
            
            # Directories should now exist
            assert docs_dir.exists()
            assert vector_dir.parent.exists()
    
    def test_config_validation(self):
        """Test configuration validation."""
        # Test invalid chunk size
        with pytest.raises(ValueError):
            ServerConfig(chunk_size=0)
        
        # Test invalid chunk overlap
        with pytest.raises(ValueError):
            ServerConfig(chunk_overlap=-1)
        
        # Test invalid similarity search k
        with pytest.raises(ValueError):
            ServerConfig(similarity_search_k=0)
    
    def test_config_serialization(self):
        """Test configuration serialization."""
        config = ServerConfig(
            name="test-server",
            version="1.0.0",
            use_fake_embeddings=True
        )
        
        # Should be able to convert to dict
        config_dict = config.model_dump()
        assert config_dict["name"] == "test-server"
        assert config_dict["version"] == "1.0.0"
        assert config_dict["use_fake_embeddings"] is True
        
        # Should be able to recreate from dict
        new_config = ServerConfig(**config_dict)
        assert new_config.name == config.name
        assert new_config.version == config.version
        assert new_config.use_fake_embeddings == config.use_fake_embeddings


class TestToolCallContext:
    """Test cases for ToolCallContext class."""
    
    def test_default_context(self):
        """Test default context creation."""
        context = ToolCallContext()
        
        assert context.request_id is None
        assert context.client_info is None
        assert context.progress_token is None
    
    def test_context_with_values(self):
        """Test context with provided values."""
        context = ToolCallContext(
            request_id="test-123",
            client_info={"name": "test-client"},
            progress_token="progress-456"
        )
        
        assert context.request_id == "test-123"
        assert context.client_info == {"name": "test-client"}
        assert context.progress_token == "progress-456"
    
    def test_context_immutability(self):
        """Test that context is properly structured."""
        context = ToolCallContext(request_id="test")
        
        # Should be able to access attributes
        assert context.request_id == "test"
        
        # Should be able to modify (dataclass is mutable by default)
        context.request_id = "modified"
        assert context.request_id == "modified"
