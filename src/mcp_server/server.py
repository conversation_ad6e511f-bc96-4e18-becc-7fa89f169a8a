"""
MCP Bible Study Server Implementation.

This module implements the main MCP server following official MCP patterns
and best practices for protocol compliance and error handling.
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional, Sequence

import mcp.types as types
from mcp.server import Server
from mcp.server.stdio import stdio_server

from .config import ServerConfig, ToolCallContext
from .services import BibleStudyService, VectorStoreService

logger = logging.getLogger(__name__)


class BibleStudyMCPServer:
    """MCP Server for Bible study tools following official MCP patterns."""
    
    def __init__(self, config: Optional[ServerConfig] = None):
        """Initialize the MCP server.
        
        Args:
            config: Server configuration, defaults to environment-based config
        """
        self.config = config or ServerConfig.from_env()
        
        # Configure logging
        logging.basicConfig(
            level=getattr(logging, self.config.log_level.upper()),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # Initialize services
        self.bible_study_service = BibleStudyService(self.config)
        self.vector_store_service = VectorStoreService(self.config)
        
        # Create MCP server instance
        self.server = Server(self.config.name)
        
        # Register handlers
        self._register_handlers()
        
        logger.info(f"MCP server '{self.config.name}' v{self.config.version} initialized")
    
    def _register_handlers(self) -> None:
        """Register MCP protocol handlers."""
        # Register tool handlers
        self._register_tool_handlers()
        
        # Register resource handlers (if needed in future)
        # self._register_resource_handlers()
        
        # Register prompt handlers (if needed in future)
        # self._register_prompt_handlers()
    
    def _register_tool_handlers(self) -> None:
        """Register tool-related handlers."""
        
        @self.server.list_tools()
        async def list_tools() -> List[types.Tool]:
            """List available tools."""
            logger.debug("Listing available tools")
            
            return [
                types.Tool(
                    name="study_method_overview",
                    description="返回圣经研习方法的详细概述，包括观察法、解释法、应用法等系统性学习方法",
                    inputSchema={
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                ),
                types.Tool(
                    name="semantic_context",
                    description="根据查询内容，在圣经研习资料中进行语义搜索，返回相关的文本段落和上下文信息",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "query": {
                                "type": "string",
                                "description": "搜索查询字符串，用中文描述要查找的内容"
                            }
                        },
                        "required": ["query"]
                    }
                )
            ]
        
        @self.server.call_tool()
        async def call_tool(
            name: str,
            arguments: Dict[str, Any]
        ) -> List[types.TextContent]:
            """Handle tool calls."""
            logger.info(f"Tool call: {name} with arguments: {arguments}")
            
            # Create context for tool call
            context = ToolCallContext()
            
            try:
                if name == "study_method_overview":
                    return await self._handle_study_method_overview(arguments, context)
                elif name == "semantic_context":
                    return await self._handle_semantic_context(arguments, context)
                else:
                    error_msg = f"Unknown tool: {name}"
                    logger.error(error_msg)
                    raise ValueError(error_msg)
                    
            except Exception as e:
                logger.error(f"Tool call failed for {name}: {e}")
                # Return error as text content rather than raising
                return [types.TextContent(
                    type="text",
                    text=f"Error executing tool '{name}': {str(e)}"
                )]
    
    async def _handle_study_method_overview(
        self,
        arguments: Dict[str, Any],
        context: ToolCallContext
    ) -> List[types.TextContent]:
        """Handle study_method_overview tool call.
        
        Args:
            arguments: Tool arguments (empty for this tool)
            context: Tool call context
            
        Returns:
            List containing text content with study method overview
        """
        try:
            overview = self.bible_study_service.get_study_method_overview(context)
            return [types.TextContent(type="text", text=overview)]
        except Exception as e:
            logger.error(f"Failed to get study method overview: {e}")
            raise
    
    async def _handle_semantic_context(
        self,
        arguments: Dict[str, Any],
        context: ToolCallContext
    ) -> List[types.TextContent]:
        """Handle semantic_context tool call.
        
        Args:
            arguments: Tool arguments containing 'query'
            context: Tool call context
            
        Returns:
            List containing text content with search results
        """
        # Validate arguments
        query = arguments.get("query")
        if not query:
            raise ValueError("Missing required argument: query")
        
        if not isinstance(query, str):
            raise ValueError("Query must be a string")
        
        # Validate query using service
        if not self.bible_study_service.validate_query(query):
            raise ValueError("Invalid query: must be a non-empty string between 2-1000 characters")
        
        try:
            # Perform semantic search
            results = self.vector_store_service.semantic_search(query, context)
            
            # Format results for MCP response
            formatted_results = self.bible_study_service.format_search_results(results)
            
            # Convert to JSON string for text content
            import json
            results_json = json.dumps(formatted_results, ensure_ascii=False, indent=2)
            
            return [types.TextContent(type="text", text=results_json)]
            
        except Exception as e:
            logger.error(f"Failed to perform semantic search: {e}")
            raise
    
    async def run_stdio(self) -> None:
        """Run the server using stdio transport."""
        logger.info("Starting MCP server with stdio transport")
        
        try:
            async with stdio_server() as (read_stream, write_stream):
                await self.server.run(
                    read_stream,
                    write_stream,
                    self.server.create_initialization_options()
                )
        except Exception as e:
            logger.error(f"Server error: {e}")
            raise
    
    def get_server_info(self) -> Dict[str, Any]:
        """Get server information for debugging.
        
        Returns:
            Dictionary with server information
        """
        return {
            "name": self.config.name,
            "version": self.config.version,
            "config": {
                "documents_dir": str(self.config.documents_dir),
                "vector_store_path": str(self.config.vector_store_path),
                "embedding_model": self.config.embedding_model_name,
                "use_fake_embeddings": self.config.use_fake_embeddings,
                "log_level": self.config.log_level,
            },
            "vector_store_info": self.vector_store_service.get_vector_store_info(),
        }


async def main() -> None:
    """Main entry point for the MCP server."""
    try:
        # Create and run server
        server = BibleStudyMCPServer()
        await server.run_stdio()
    except KeyboardInterrupt:
        logger.info("Server shutdown requested")
    except Exception as e:
        logger.error(f"Server failed to start: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
