"""
Vector Store Service.

This module provides vector store functionality for semantic search,
independent of the MCP protocol implementation.
"""

import logging
import os
from pathlib import Path
from typing import List, Dict, Any, Optional

from langchain_community.document_loaders import PyMuPDFLoader
from langchain_community.vectorstores import FAISS
from langchain_community.embeddings import HuggingFaceEmbeddings, FakeEmbeddings
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_core.embeddings import Embeddings

from ..config import ServerConfig, ToolCallContext

logger = logging.getLogger(__name__)


class VectorStoreService:
    """Service for vector store operations and semantic search."""
    
    def __init__(self, config: ServerConfig):
        """Initialize the vector store service.

        Args:
            config: Server configuration
        """
        self.config = config
        self.embeddings: Optional[Embeddings] = None
        self.vector_store: Optional[FAISS] = None
        
        # Initialize components
        self._initialize_embeddings()
        self._initialize_vector_store()
        
        logger.info("Vector store service initialized")
    
    def _initialize_embeddings(self) -> None:
        """Initialize the embedding model."""
        try:
            if self.config.use_fake_embeddings:
                logger.warning("Using FakeEmbeddings for testing - not suitable for production")
                self.embeddings = FakeEmbeddings(size=768)
            else:
                logger.info(f"Loading embedding model: {self.config.embedding_model_name}")
                self.embeddings = HuggingFaceEmbeddings(
                    model_name=self.config.embedding_model_name
                )
                logger.info("Embedding model loaded successfully")
        except Exception as e:
            logger.error(f"Failed to initialize embeddings: {e}")
            raise RuntimeError(f"Failed to initialize embeddings: {e}")
    
    def _initialize_vector_store(self) -> None:
        """Initialize or load the vector store."""
        try:
            if self._vector_store_exists():
                self._load_existing_vector_store()
            else:
                self._create_new_vector_store()
        except Exception as e:
            logger.error(f"Failed to initialize vector store: {e}")
            raise RuntimeError(f"Failed to initialize vector store: {e}")
    
    def _vector_store_exists(self) -> bool:
        """Check if vector store files exist."""
        return (
            self.config.vector_store_path.exists() and
            (self.config.vector_store_path / "index.faiss").exists() and
            (self.config.vector_store_path / "index.pkl").exists()
        )
    
    def _load_existing_vector_store(self) -> None:
        """Load existing vector store from disk."""
        logger.info("Loading existing vector store")
        try:
            if self.embeddings is None:
                raise RuntimeError("Embeddings not initialized")
            self.vector_store = FAISS.load_local(
                str(self.config.vector_store_path),
                self.embeddings,
                allow_dangerous_deserialization=True
            )
            logger.info("Vector store loaded successfully")
        except Exception as e:
            logger.warning(f"Failed to load existing vector store: {e}")
            logger.info("Creating new vector store instead")
            self._create_new_vector_store()
    
    def _create_new_vector_store(self) -> None:
        """Create a new vector store from documents."""
        logger.info("Creating new vector store from documents")
        
        # Ensure directories exist
        self.config.ensure_directories()
        
        # Get PDF files
        pdf_files = self._get_pdf_files()
        
        if not pdf_files:
            logger.warning("No PDF files found, creating empty vector store")
            if self.embeddings is None:
                raise RuntimeError("Embeddings not initialized")
            self.vector_store = FAISS.from_texts(
                ["No documents available"],
                self.embeddings
            )
            return
        
        # Load and process documents
        documents = self._load_documents(pdf_files)
        
        if not documents:
            logger.warning("No documents loaded, creating empty vector store")
            if self.embeddings is None:
                raise RuntimeError("Embeddings not initialized")
            self.vector_store = FAISS.from_texts(
                ["No documents available"],
                self.embeddings
            )
            return

        # Split documents into chunks
        chunks = self._split_documents(documents)

        # Create vector store
        if self.embeddings is None:
            raise RuntimeError("Embeddings not initialized")
        self.vector_store = FAISS.from_documents(chunks, self.embeddings)
        
        # Save vector store
        self._save_vector_store()
        
        logger.info(f"Vector store created with {len(chunks)} chunks from {len(documents)} document pages")
    
    def _get_pdf_files(self) -> List[Path]:
        """Get list of PDF files in documents directory."""
        if not self.config.documents_dir.exists():
            logger.warning(f"Documents directory does not exist: {self.config.documents_dir}")
            return []
        
        pdf_files = [
            file for file in self.config.documents_dir.iterdir()
            if file.suffix.lower() == '.pdf' and file.is_file()
        ]
        
        logger.info(f"Found {len(pdf_files)} PDF files")
        return pdf_files
    
    def _load_documents(self, pdf_files: List[Path]) -> List[Any]:
        """Load documents from PDF files."""
        documents = []
        
        for pdf_file in pdf_files:
            try:
                logger.info(f"Processing PDF: {pdf_file}")
                loader = PyMuPDFLoader(str(pdf_file))
                file_documents = loader.load()
                documents.extend(file_documents)
                logger.debug(f"Loaded {len(file_documents)} pages from {pdf_file}")
            except Exception as e:
                logger.error(f"Error processing {pdf_file}: {e}")
        
        logger.info(f"Loaded {len(documents)} total document pages")
        return documents
    
    def _split_documents(self, documents: List[Any]) -> List[Any]:
        """Split documents into chunks."""
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=self.config.chunk_size,
            chunk_overlap=self.config.chunk_overlap,
            separators=["\n\n", "\n", "。", "，", " ", ""],
            length_function=len,
        )
        
        chunks = text_splitter.split_documents(documents)
        logger.info(f"Split documents into {len(chunks)} chunks")
        return chunks
    
    def _save_vector_store(self) -> None:
        """Save vector store to disk."""
        try:
            if self.vector_store is None:
                logger.warning("No vector store to save")
                return
            self.config.vector_store_path.mkdir(parents=True, exist_ok=True)
            self.vector_store.save_local(str(self.config.vector_store_path))
            logger.info(f"Vector store saved to {self.config.vector_store_path}")
        except Exception as e:
            logger.error(f"Failed to save vector store: {e}")
            # Don't raise exception as this is not critical for operation
    
    def semantic_search(self, query: str, context: ToolCallContext) -> List[Dict[str, Any]]:  # noqa: ARG002
        """Perform semantic search on the vector store.
        
        Args:
            query: Search query string
            context: Tool call context information
            
        Returns:
            List of search results with text, metadata, and scores
            
        Raises:
            ValueError: If query is invalid or search fails
        """
        if not query or not isinstance(query, str):
            raise ValueError("Query must be a non-empty string")
        
        if not self.vector_store:
            raise ValueError("Vector store not initialized")
        
        logger.info(f"Performing semantic search for query: {query[:50]}...")
        
        try:
            # Perform similarity search
            results = self.vector_store.similarity_search(
                query,
                k=self.config.similarity_search_k
            )
            
            # Format results
            formatted_results = []
            for doc in results:
                result = {
                    "text": doc.page_content,
                    "metadata": doc.metadata,
                    "score": getattr(doc, "score", None)
                }
                formatted_results.append(result)
            
            logger.info(f"Found {len(formatted_results)} search results")
            return formatted_results
            
        except Exception as e:
            logger.error(f"Semantic search failed: {e}")
            raise ValueError(f"Semantic search failed: {e}")
    
    def get_vector_store_info(self) -> Dict[str, Any]:
        """Get information about the vector store.
        
        Returns:
            Dictionary with vector store information
        """
        if not self.vector_store:
            return {"status": "not_initialized"}
        
        try:
            # Get basic info
            info = {
                "status": "initialized",
                "embedding_model": self.config.embedding_model_name,
                "use_fake_embeddings": self.config.use_fake_embeddings,
                "vector_store_path": str(self.config.vector_store_path),
                "documents_dir": str(self.config.documents_dir),
            }
            
            # Try to get document count (may not be available in all FAISS versions)
            try:
                info["document_count"] = self.vector_store.index.ntotal
            except AttributeError:
                info["document_count"] = "unknown"
            
            return info
            
        except Exception as e:
            logger.error(f"Failed to get vector store info: {e}")
            return {"status": "error", "error": str(e)}
