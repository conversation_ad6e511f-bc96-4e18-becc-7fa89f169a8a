"""
Bible Study Service.

This module provides the core business logic for Bible study methods and content,
independent of the MCP protocol implementation.
"""

import logging
from typing import Dict, Any, List

from ..config import ServerConfig, ToolCallContext

logger = logging.getLogger(__name__)


class BibleStudyService:
    """Service for Bible study methods and content."""
    
    def __init__(self, config: ServerConfig):
        """Initialize the Bible study service.
        
        Args:
            config: Server configuration
        """
        self.config = config
        logger.info("Bible study service initialized")
    
    def get_study_method_overview(self, context: ToolCallContext) -> str:
        """Get an overview of Bible study methods in Chinese.
        
        Args:
            context: Tool call context information
            
        Returns:
            Detailed overview of Bible study methods in Chinese
            
        Raises:
            ValueError: If the operation fails
        """
        logger.info("Generating Bible study method overview")
        
        try:
            overview = """圣经研习方法概述：

一、观察法（What）：
1. 仔细阅读经文，了解基本事实
2. 找出关键词、重复的词语和主题
3. 注意时间、地点、人物和事件的细节
4. 寻找文学结构和上下文关系

二、解释法（Why）：
1. 了解历史背景和文化环境
2. 分析词语的原意和用法
3. 考虑文体特点（叙事、诗歌、书信等）
4. 对照其他相关经文进行比较

三、应用法（How）：
1. 思考经文对原读者的意义
2. 找出普遍原则和永恒真理
3. 将这些原则应用到当代生活中
4. 制定具体的实践步骤

四、祷告默想：
1. 以祷告开始和结束研经过程
2. 默想经文中的属灵教训
3. 寻求圣灵的引导和启示
4. 谦卑接受神的话语

五、小组讨论：
1. 分享个人见解和领悟
2. 倾听他人的观点和经历
3. 一起探讨难解之处
4. 彼此鼓励实践真理

六、研经工具的使用：
1. 原文词典和注释书
2. 历史背景资料
3. 地图和图表
4. 不同译本的比较

七、系统性学习：
1. 按卷逐章研读
2. 专题式研究
3. 人物传记研究
4. 教义系统学习

有效的圣经学习需要恒心、祷告和圣灵的带领。通过持续的学习和实践，
我们能够更深入地理解神的话语，并将其应用到日常生活中。"""
            
            logger.info("Bible study method overview generated successfully")
            return overview
            
        except Exception as e:
            logger.error(f"Failed to generate study method overview: {e}")
            raise ValueError(f"Failed to generate study method overview: {e}")
    
    def validate_query(self, query: str) -> bool:
        """Validate a search query.
        
        Args:
            query: The query string to validate
            
        Returns:
            True if query is valid, False otherwise
        """
        if not query or not isinstance(query, str):
            return False
        
        # Check minimum length
        if len(query.strip()) < 2:
            return False
        
        # Check maximum length (reasonable limit)
        if len(query) > 1000:
            return False
        
        return True
    
    def format_search_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Format search results for consistent output.
        
        Args:
            results: Raw search results from vector store
            
        Returns:
            Formatted search results
        """
        formatted_results = []
        
        for result in results:
            formatted_result = {
                "text": result.get("text", ""),
                "metadata": result.get("metadata", {}),
                "score": result.get("score")
            }
            formatted_results.append(formatted_result)
        
        return formatted_results

    def get_study_tips(self) -> List[str]:
        """Get practical Bible study tips.

        Returns:
            List of practical study tips
        """
        return [
            "选择固定的时间和地点进行研经",
            "准备好笔记本记录重要发现",
            "从祷告开始，求神开启心窍",
            "先读完整段落，再深入细节",
            "注意经文的上下文关系",
            "查考原文词汇的含义",
            "参考多种译本进行比较",
            "思考经文的实际应用",
            "与其他信徒分享和讨论",
            "将学到的真理付诸实践"
        ]
