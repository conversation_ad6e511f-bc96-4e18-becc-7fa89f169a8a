"""
Configuration management for the MCP Bible Study Server.

This module provides configuration classes and utilities for managing
server settings, paths, and environment variables.
"""

import os
from dataclasses import dataclass
from pathlib import Path
from typing import Optional

from pydantic import BaseModel, Field, field_validator


class ServerConfig(BaseModel):
    """Configuration for the MCP Bible Study Server."""
    
    # Server identification
    name: str = Field(default="bible-study-mcp-server", description="Server name")
    version: str = Field(default="0.1.0", description="Server version")
    
    # Paths
    documents_dir: Path = Field(
        default_factory=lambda: Path(__file__).parent.parent.parent / "documents",
        description="Directory containing PDF documents"
    )
    vector_store_path: Path = Field(
        default_factory=lambda: Path(__file__).parent.parent.parent / "vector_store",
        description="Path to vector store files"
    )
    
    # Embedding model configuration
    embedding_model_name: str = Field(
        default="jinaai/jina-embeddings-v2-base-zh",
        description="Name of the embedding model to use"
    )
    use_fake_embeddings: bool = Field(
        default=False,
        description="Use fake embeddings for testing (faster, offline)"
    )
    
    # Vector store configuration
    chunk_size: int = Field(default=1000, description="Text chunk size for splitting")
    chunk_overlap: int = Field(default=100, description="Overlap between text chunks")
    similarity_search_k: int = Field(default=3, description="Number of similar documents to return")
    
    # Logging configuration
    log_level: str = Field(default="INFO", description="Logging level")

    @field_validator('chunk_size')
    @classmethod
    def validate_chunk_size(cls, v):
        if v <= 0:
            raise ValueError('chunk_size must be positive')
        return v

    @field_validator('chunk_overlap')
    @classmethod
    def validate_chunk_overlap(cls, v):
        if v < 0:
            raise ValueError('chunk_overlap must be non-negative')
        return v

    @field_validator('similarity_search_k')
    @classmethod
    def validate_similarity_search_k(cls, v):
        if v <= 0:
            raise ValueError('similarity_search_k must be positive')
        return v
    
    @classmethod
    def from_env(cls) -> "ServerConfig":
        """Create configuration from environment variables."""
        return cls(
            name=os.getenv("MCP_SERVER_NAME", "bible-study-mcp-server"),
            version=os.getenv("MCP_SERVER_VERSION", "0.1.0"),
            documents_dir=Path(os.getenv("MCP_DOCUMENTS_DIR", cls().documents_dir)),
            vector_store_path=Path(os.getenv("MCP_VECTOR_STORE_PATH", cls().vector_store_path)),
            embedding_model_name=os.getenv("MCP_EMBEDDING_MODEL", cls().embedding_model_name),
            use_fake_embeddings=os.getenv("MCP_USE_FAKE_EMBEDDINGS", "false").lower() == "true",
            chunk_size=int(os.getenv("MCP_CHUNK_SIZE", "1000")),
            chunk_overlap=int(os.getenv("MCP_CHUNK_OVERLAP", "100")),
            similarity_search_k=int(os.getenv("MCP_SIMILARITY_K", "3")),
            log_level=os.getenv("MCP_LOG_LEVEL", "INFO"),
        )
    
    def ensure_directories(self) -> None:
        """Ensure required directories exist."""
        self.documents_dir.mkdir(parents=True, exist_ok=True)
        self.vector_store_path.parent.mkdir(parents=True, exist_ok=True)


@dataclass
class ToolCallContext:
    """Context information for tool calls."""
    
    request_id: Optional[str] = None
    client_info: Optional[dict] = None
    progress_token: Optional[str] = None
