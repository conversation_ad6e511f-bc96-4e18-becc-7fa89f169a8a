"""
MCP Bible Study Server Package.

This package provides a Model Context Protocol (MCP) server implementation
for Bible study tools in Chinese, following official MCP best practices.
"""

__version__ = "0.1.0"
__author__ = "Bible Study MCP Server Team"

from .server import BibleStudyMCPServer
from .services import BibleStudyService, VectorStoreService
from .config import ServerConfig

__all__ = [
    "BibleStudyMCPServer",
    "BibleStudyService", 
    "VectorStoreService",
    "ServerConfig",
]
