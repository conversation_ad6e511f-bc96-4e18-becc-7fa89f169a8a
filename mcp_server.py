#!/usr/bin/env python3
"""
MCP Bible Study Server Entry Point.

This is the main entry point for the MCP Bible Study Server, following
official MCP patterns and best practices.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.mcp_server import BibleStudyMCPServer, ServerConfig

logger = logging.getLogger(__name__)


def setup_logging(log_level: str = "INFO") -> None:
    """Setup logging configuration.
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR)
    """
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stderr)  # Use stderr to avoid interfering with stdio transport
        ]
    )


async def main() -> None:
    """Main entry point for the MCP server."""
    try:
        # Load configuration from environment
        config = ServerConfig.from_env()
        
        # Setup logging
        setup_logging(config.log_level)
        
        logger.info(f"Starting {config.name} v{config.version}")
        logger.info(f"Documents directory: {config.documents_dir}")
        logger.info(f"Vector store path: {config.vector_store_path}")
        logger.info(f"Embedding model: {config.embedding_model_name}")
        
        # Ensure required directories exist
        config.ensure_directories()
        
        # Create and run server
        server = BibleStudyMCPServer(config)
        
        # Log server info for debugging
        server_info = server.get_server_info()
        logger.debug(f"Server info: {server_info}")
        
        # Run server with stdio transport
        await server.run_stdio()
        
    except KeyboardInterrupt:
        logger.info("Server shutdown requested by user")
    except Exception as e:
        logger.error(f"Server failed: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
