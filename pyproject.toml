[project]
name = "triple3-mcp-server"
version = "0.1.0"
description = "MCP server for Bible study tools"
authors = [
    {name = "User"}
]
readme = "README.md"
requires-python = ">=3.11,<3.12"
dependencies = [
    "mcp[cli] (>=1.9.3,<2.0.0)",
    "langchain (>=0.3.25,<0.4.0)",
    "langchain-community (>=0.3.25,<0.4.0)",
    "langchain-text-splitters (>=0.3.8,<0.4.0)",
    "transformers (>=4.52.4,<5.0.0)",
    "faiss-cpu (>=1.11.0,<2.0.0)",
    "pymupdf (>=1.26.0,<2.0.0)",
    "pydantic (>=2.11.5,<3.0.0)",
    "python-dotenv (>=1.1.0,<2.0.0)",
    "numpy (>=2.3.0,<3.0.0)",
    "torch (>=2.7.1,<3.0.0)",
    "sentence-transformers (>=4.1.0,<5.0.0)",
    "httpx (>=0.28.1,<0.29.0)",
    "pytest (>=8.0.0,<9.0.0)",
    "pytest-asyncio (>=0.24.0,<0.25.0)"
]


[tool.poetry]
packages = [{include = "src"}]

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
